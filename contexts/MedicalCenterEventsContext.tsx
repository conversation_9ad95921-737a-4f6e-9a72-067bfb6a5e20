"use client"

import React, {createContext, useContext, useEffect, useRef, useState} from 'react'
import {SSEEvent, useMedicalCenterSSE} from '@/hooks/useMedicalCenterSSE'
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {PatientResponse} from "@/types/patient/patientResponse";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {AppointmentState, ProfessionalSchedulesResponse} from "@/types/professional-schedules";

export interface MedicalCenterEventData {
    patients: PatientResponse[],
    doctors: DoctorsForMedicalCenter[]
}

export interface EventHandler {
    name: string
    description: string
    handler: (data: unknown, eventManager?: MedicalCenterEventManager) => void
}

export class MedicalCenterEventManager {
    public dataStore: MedicalCenterEventData | undefined
    private handlers: Map<string, EventHandler[]> = new Map()

    registerHandler(eventType: string, handler: EventHandler) {
        const handlers = this.handlers.get(eventType) || []
        handlers.push(handler)
        this.handlers.set(eventType, handlers)
    }

    removeHandler(eventType: string, handlerName: string) {
        const handlers = this.handlers.get(eventType) || []
        const filteredHandlers = handlers.filter(h => h.name !== handlerName)
        this.handlers.set(eventType, filteredHandlers)
    }

    handleEvent(eventType: string, data: unknown) {
        const handlers = this.handlers.get(eventType) || []
        handlers.forEach(handler => {
            try {
                handler.handler(data, this)
            } catch (error) {
                console.error(`Error in handler ${handler.name} for event ${eventType}:`, error)
            }
        })
    }

    getDataStore(): MedicalCenterEventData | undefined {
        return this.dataStore
    }

    updateDataStore<K extends keyof MedicalCenterEventData>(key: K, data: MedicalCenterEventData[K]) {
        if (!this.dataStore) {
            this.dataStore = {
                patients: [],
                doctors: []
            }
        }
        this.dataStore[key] = data
    }

}

const defaultEventHandlers = {
    patientsHandler: {
        name: 'patientsHandler',
        description: 'Handles initial patient data',
        handler: (data: unknown, eventManager?: MedicalCenterEventManager) => {
            if (eventManager && Array.isArray(data)) {
                const patients = data.map(patientData => PatientResponse.fromJSON(patientData));
                eventManager.updateDataStore('patients', patients);
            }
        }
    },
    doctorInformationHandler: {
        name: 'doctorInformationHandler',
        description: 'Handles initial doctor information',
        handler: (data: unknown, eventManager?: MedicalCenterEventManager) => {
            if (eventManager && Array.isArray(data)) {
                const doctors = data.map(doctorData => DoctorsForMedicalCenter.fromJSON(doctorData));
                eventManager.updateDataStore('doctors', doctors);
            }
        }
        // TODO : note for future appointment schedules + appointment handling: if datastore doctor has month in record then change, if not ignore
    },
    patientInformationHandler: {
        name: 'patientInformationHandler',
        description: 'Handles patient information updates',
        handler: (data: unknown, eventManager?: MedicalCenterEventManager) => {
            if (eventManager && eventManager.dataStore) {
                const previousPatients = eventManager.dataStore.patients;
                const patient = PatientResponse.fromJSON(data as Record<string, unknown>);
                const patients = previousPatients.filter(p => p.id !== patient.id).concat(patient);
                eventManager.updateDataStore('patients', patients);
            }
        }
    },
    appointmentInformationHandler: {
        name: 'appointmentInformationHandler',
        description: 'Handles appointment information updates',
        handler: (data: unknown, eventManager?: MedicalCenterEventManager) => {
            if (!eventManager?.dataStore?.doctors) return;
            const updatedAppointment = ProfessionalAppointment.fromJSON(data as Record<string, unknown>);
            const [year, month] = updatedAppointment.date.split('-');
            const yearAndMonthFormat = `${year}-${month}`;
            const newDoctors = eventManager.dataStore.doctors.map(doctor => {
                if (doctor.id !== updatedAppointment.doctorId || !doctor.agendaByMonthAndYear[yearAndMonthFormat]) {
                    return doctor;
                }
                const newAppointments: ProfessionalAppointment[] = doctor.agendaByMonthAndYear[yearAndMonthFormat].appointments.filter(apt => apt.id !== updatedAppointment.id);
                if (updatedAppointment.state !== AppointmentState.CANCELLED) {
                    newAppointments.push(updatedAppointment);
                }
                const updatedAgenda: ProfessionalSchedulesResponse = doctor.agendaByMonthAndYear[yearAndMonthFormat];
                updatedAgenda.appointments = newAppointments;
                const newDoctor = doctor
                doctor.agendaByMonthAndYear[yearAndMonthFormat] = updatedAgenda
                return newDoctor
            });
            eventManager.updateDataStore('doctors', newDoctors);
        }
    }
}

interface MedicalCenterEventsContextType {
    isConnected: boolean
    error: string | null
    lastEvent: SSEEvent | null
    eventManager: MedicalCenterEventManager | null
    dataStore: MedicalCenterEventData | undefined
    disconnect: () => void
    reconnect: () => void
    initialize: (medicalCenterId: number, employeeUserId: number) => void
    isInitialized: boolean
}

const MedicalCenterEventsContext = createContext<MedicalCenterEventsContextType | undefined>(undefined)

interface MedicalCenterEventsProviderProps {
    children: React.ReactNode
}

export const MedicalCenterEventsProvider: React.FC<MedicalCenterEventsProviderProps> = ({children}) => {
    const [isInitialized, setIsInitialized] = useState(false)
    const [medicalCenterId, setMedicalCenterId] = useState<number | null>(null)
    const [employeeUserId, setEmployeeUserId] = useState<number | null>(null)
    const eventManagerRef = useRef<MedicalCenterEventManager | null>(null)

    const {
        isConnected,
        error,
        lastEvent,
        addEventListener,
        disconnect,
        reconnect
    } = useMedicalCenterSSE({
        medicalCenterId: medicalCenterId || 0,
        employeeUserId: employeeUserId || 0
    })

    const initialize = (newMedicalCenterId: number, newEmployeeUserId: number) => {
        if (medicalCenterId === newMedicalCenterId && employeeUserId === newEmployeeUserId && isInitialized) {
            return
        }

        setMedicalCenterId(newMedicalCenterId)
        setEmployeeUserId(newEmployeeUserId)

        eventManagerRef.current = new MedicalCenterEventManager()

        eventManagerRef.current.registerHandler('patients', defaultEventHandlers.patientsHandler)
        eventManagerRef.current.registerHandler('doctors', defaultEventHandlers.doctorInformationHandler)
        eventManagerRef.current.registerHandler('patient', defaultEventHandlers.patientInformationHandler)
        eventManagerRef.current.registerHandler('appointment', defaultEventHandlers.appointmentInformationHandler)

        setIsInitialized(true)
    }

    useEffect(() => {
        if (!isInitialized || !eventManagerRef.current) return

        const eventManager = eventManagerRef.current

        const cleanup = [
            addEventListener('patients', (data) => eventManager.handleEvent('patients', data)),
            addEventListener('doctors', (data) => eventManager.handleEvent('doctors', data)),
            addEventListener('patient', (data) => eventManager.handleEvent('patient', data)),
            addEventListener('appointment', (data) => eventManager.handleEvent('appointment', data)),
            addEventListener('schedules', (data) => eventManager.handleEvent('schedules', data)),
            addEventListener('connection', (data) => eventManager.handleEvent('connection', data)),
            addEventListener('error', (data) => eventManager.handleEvent('error', data)),
            addEventListener('message', (data) => eventManager.handleEvent('message', data))
        ]

        return () => {
            cleanup.forEach(cleanupFn => cleanupFn())
        }
    }, [isInitialized, addEventListener])

    const value: MedicalCenterEventsContextType = {
        isConnected,
        error,
        lastEvent,
        eventManager: eventManagerRef.current,
        dataStore: eventManagerRef.current?.getDataStore(),
        disconnect,
        reconnect,
        initialize,
        isInitialized
    }

    return (
        <MedicalCenterEventsContext.Provider value={value}>
            {children}
        </MedicalCenterEventsContext.Provider>
    )
}

export const useMedicalCenterEventsContext = () => {
    const context = useContext(MedicalCenterEventsContext)
    if (context === undefined) {
        throw new Error('useMedicalCenterEventsContext must be used within a MedicalCenterEventsProvider')
    }
    return context
}
