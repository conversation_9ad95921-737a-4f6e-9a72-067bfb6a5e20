"use client"

import { createContext, useState, ReactNode } from "react"
import { Specialty } from "@/data/specialties"

interface NewDoctorContextProps {
  newDoctor: {
    firstName: string;
    lastName: string;
    email: string;
    specialties: Specialty[];
  }
  setNewDoctor: (doctor: {
    firstName: string;
    lastName: string;
    email: string;
    specialties: Specialty[];
  }) => void
  isNewDoctorDialogOpen: boolean
  setIsNewDoctorDialogOpen: (open: boolean) => void
  newSpecialty: string
  setNewSpecialty: (specialty: string) => void
  addSpecialty: () => void
  removeSpecialty: (specialty: string) => void
}

export const NewDoctorContext = createContext<NewDoctorContextProps>({} as NewDoctorContextProps)

export function NewDoctorProvider({ children }: { children: ReactNode }) {
  const [newDoctor, setNewDoctor] = useState({
    firstName: "",
    lastName: "",
    email: "",
    specialties: [] as Specialty[],
  })
  const [isNewDoctorDialogOpen, setIsNewDoctorDialogOpen] = useState(false)
  const [newSpecialty, setNewSpecialty] = useState("")

  const addSpecialty = () => {
    if (newSpecialty && !newDoctor.specialties.includes(newSpecialty as Specialty)) {
      setNewDoctor({ ...newDoctor, specialties: [...newDoctor.specialties, newSpecialty as Specialty] })
      setNewSpecialty("")
    }
  }

  const removeSpecialty = (specialty: string) => {
    setNewDoctor({ ...newDoctor, specialties: newDoctor.specialties.filter((s) => s !== specialty) })
  }

  return (
    <NewDoctorContext.Provider
      value={{
        newDoctor,
        setNewDoctor,
        isNewDoctorDialogOpen,
        setIsNewDoctorDialogOpen,
        newSpecialty,
        setNewSpecialty,
        addSpecialty,
        removeSpecialty,
      }}
    >
      {children}
    </NewDoctorContext.Provider>
  )
}