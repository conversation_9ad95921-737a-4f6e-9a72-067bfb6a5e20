import {ProfessionalSchedulesResponse} from "@/types/professional-schedules";
import {convertTimeStringToMinutes} from "@/utils/dateUtils";
import {HealthInsuranceForDoctor} from "@/types/healthInsurance/HealthInsuranceForDoctor";
import {ConsultationType} from "@/types/consultationTypes/ConsultationType";
import {BookingPolicies} from "@/types/bookingPolicies/BookingPolicies";
import {
    AppointmentCreationRequestHealthInsuranceInformation
} from "@/app/api/requestBodies/AppointmentCreationRequestHealthInsuranceInformation";

export class DoctorsForMedicalCenter {
    constructor(
        public id: number,
        public name: string,
        public surname: string,
        public specialties: string[],
        public medicalLicense: string,
        public nextAppointment: string | null,
        public nextAppointmentQuantity: number | null,
        public agendaByMonthAndYear: Record<string, ProfessionalSchedulesResponse>,
        public appointmentIntervalTime: string,
        public healthInsurances: HealthInsuranceForDoctor[],
        public consultationTypes: ConsultationType[],
        public bookingPolicies: BookingPolicies
    ) {
    }

    get fullName(): string {
        const firstName = this.name || '';
        const lastName = this.surname || '';
        return `${firstName} ${lastName}`.trim() || 'Unknown';
    }

    get appointmentDuration(): number {
        return convertTimeStringToMinutes(this.appointmentIntervalTime);
    }

    static fromJSON(json: Record<string, unknown>): DoctorsForMedicalCenter {
        const response: ProfessionalSchedulesResponse = ProfessionalSchedulesResponse.fromJSON(json.agenda as Record<string, unknown>);
        const agendaByMonthAndYear: Record<string, ProfessionalSchedulesResponse> = {};
        agendaByMonthAndYear[response.yearAndMonthFormat] = response;
        return new DoctorsForMedicalCenter(
            json.id as number,
            json.name as string,
            json.surname as string,
            Array.isArray(json.specialties) ? json.specialties : [],
            json.medicalLicense as string,
            json.nextAppointment as string | null,
            json.nextAppointmentQuantity as number | null,
            agendaByMonthAndYear,
            json.appointmentIntervalTime as string,
            Array.isArray(json.healthInsurances) ? json.healthInsurances.map(hi => HealthInsuranceForDoctor.fromJSON(hi)) : [],
            Array.isArray(json.consultationTypes) ? json.consultationTypes.map(ct => ConsultationType.fromJSON(ct)) : [],
            BookingPolicies.fromJSON(json.bookingPolicies as Record<string, unknown>)
        );
    }

    isHealthInsuranceAccepted(healthInsuranceInformation: AppointmentCreationRequestHealthInsuranceInformation | null | undefined) {
        return !!healthInsuranceInformation && this.healthInsurances.some(hi => hi.name === healthInsuranceInformation.name && hi.plans.some(p => p.name === healthInsuranceInformation.plan && p.isAccepted));
    }

    isHealthInsuranceIdAccepted(healthInsuranceId: number | null) {
        return !!healthInsuranceId && this.healthInsurances.some(healthInsurance => healthInsurance.plans.some(plan => plan.id === healthInsuranceId && plan.isAccepted));
    }
}