export class PatientAppointment {
    constructor(
        public id: number,
        public doctorId: number,
        public date: string,
        public startTime: string,
        public state: string,
        public dateTime: string,
        public consultationType: string
    ) {
    }

    static fromJSON(json: Record<string, unknown>): PatientAppointment {
        return new PatientAppointment(
            json.id as number,
            json.doctorId as number,
            json.date as string,
            json.startTime as string,
            json.state as string,
            json.dateTime as string,
            json.consultationType as string
        );
    }
}