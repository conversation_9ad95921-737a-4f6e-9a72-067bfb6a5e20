import {PatientAppointment} from "@/types/patient/patientAppointment";
import {
    AppointmentCreationRequestHealthInsuranceInformation
} from "@/app/api/requestBodies/AppointmentCreationRequestHealthInsuranceInformation";
import {
    PatientCreationRequestHealthInsuranceInformation
} from "@/app/api/requestBodies/PatientCreationRequestFromMedicalCenter";

export class PatientHealthInsurance {
    constructor(
        public id: number,
        public name: string,
        public plan: string
    ) {
    }

    get fullName(): string {
        return `${this.name}  ${this.plan}`.trim();
    }

    static fromJSON(json: Record<string, unknown>): PatientHealthInsurance {
        return new PatientHealthInsurance(
            json.id as number,
            json.name as string,
            json.plan as string
        );
    }

    toAppointmentCreationRequestHealthInsuranceInformation(): AppointmentCreationRequestHealthInsuranceInformation {
        return {
            name: this.name,
            plan: this.plan
        };
    }

    toPatientCreationRequestHealthInsuranceInformation(): PatientCreationRequestHealthInsuranceInformation {
        return {
            name: this.name,
            plan: this.plan
        };
    }
}


export class PatientResponse {
    constructor(
        public id: number,
        public name: string,
        public userId: number | null,
        public phone: string,
        public email: string,
        public healthInsurance: PatientHealthInsurance | null,
        public identificationNumber: string,
        public attentionsInMedicalCenter: number,
        public attendancePercentage: number,
        public futureAppointments: PatientAppointment[],
        public pastAppointments: PatientAppointment[]
    ) {
    }

    get cobertura(): string {
        return this.healthInsurance ? this.healthInsurance.fullName : "Sin Cobertura";
    }

    static fromJSON(json: Record<string, unknown>): PatientResponse {
        return new PatientResponse(
            json.id as number,
            json.name as string,
            json.userId as number | null,
            json.phone as string,
            json.email as string,
            json.healthInsurance ? PatientHealthInsurance.fromJSON(json.healthInsurance as Record<string, unknown>) : null,
            json.identificationNumber as string,
            json.attentionsInMedicalCenter as number,
            json.attendancePercentage as number,
            Array.isArray(json.futureAppointments)
                ? json.futureAppointments.map(appointment => PatientAppointment.fromJSON(appointment))
                : [],
            Array.isArray(json.pastAppointments)
                ? json.pastAppointments.map(appointment => PatientAppointment.fromJSON(appointment))
                : []
        );
    }

}