import {ConsultationHealthInsuranceExtraProperties} from './ConsultationHealthInsuranceExtraProperties';
import {ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";
import {
    AppointmentCreationRequestHealthInsuranceInformation
} from "@/app/api/requestBodies/AppointmentCreationRequestHealthInsuranceInformation";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";


export class AppointmentConsultationType {
    constructor(
        public consultationTypeId: number,
        public name: string,
    ) {
    }

    static fromJSON(json: Record<string, unknown>): AppointmentConsultationType {
        return new AppointmentConsultationType(
            json.consultationTypeId as number,
            json.name as string
        );
    }
}


export class ConsultationType {
    constructor(
        public consultationTypeId: number,
        public name: string,
        public price: number,
        public availableOnline: boolean,
        public acceptsSelfPaidPatient: boolean,
        public requiresMedicalOrder: boolean,
        public appointmentIntervalAmount: number,
        public instructions: string,
        public dailyLimit: number | null,
        public healthInsuranceExtraProperties: ConsultationHealthInsuranceExtraProperties[],
    ) {
    }

    static fromJSON(json: Record<string, unknown>): ConsultationType {
        const healthInsuranceExtraProperties = Array.isArray(json.healthInsuranceExtraProperties)
            ? (json.healthInsuranceExtraProperties as Record<string, unknown>[]).map((prop) => ConsultationHealthInsuranceExtraProperties.fromJSON(prop))
            : [];

        return new ConsultationType(
            json.consultationTypeId as number,
            json.name as string,
            json.price as number,
            json.availableOnline as boolean,
            json.acceptsSelfPaidPatient as boolean,
            json.requiresMedicalOrder as boolean,
            json.appointmentIntervalAmount as number,
            json.instructions as string,
            json.dailyLimit as number | null,
            healthInsuranceExtraProperties
        );
    }

    hasInstructions() {
        return this.instructions != null && this.instructions.trim().length > 0;
    }

    hasConsultationTypeInfo(healthInsuranceId: number | null): boolean {
        if (this.hasInstructions() || this.requiresMedicalOrder || !healthInsuranceId) {
            return true;
        }
        const healthInsuranceExtraProperty = this.healthInsuranceExtraProperties.find(
            (property) => property.healthInsuranceId === healthInsuranceId
        );
        return !!healthInsuranceExtraProperty;


    }


    getHealthInsurancePropertyForMaybeHealthInsuranceId(healthInsuranceId: number | null): ConsultationHealthInsuranceExtraProperties | null {
        if (!healthInsuranceId) {
            return null;
        }
        return this.healthInsuranceExtraProperties.find(
            (property) => property.healthInsuranceId === healthInsuranceId) || null;
    }

    getHealthInsurancePropertyForMaybeHealthInsuranceInformation(healthInsuranceInformation: AppointmentCreationRequestHealthInsuranceInformation | null): ConsultationHealthInsuranceExtraProperties | null {
        if (!healthInsuranceInformation) {
            return null;
        }
        return this.healthInsuranceExtraProperties.find(
            (property) => property.name === healthInsuranceInformation.name && property.plan === healthInsuranceInformation.plan
        ) || null;
    }

    toAppointmentConsultationType() {
        return new AppointmentConsultationType(this.consultationTypeId, this.name);
    }

    getPriceForHealthInsurance(healthInsuranceInformation: AppointmentCreationRequestHealthInsuranceInformation | undefined, healthInsuranceIsAccepted: boolean) {
        if (!healthInsuranceInformation || !healthInsuranceIsAccepted) {
            return this.price;
        }
        const healthInsuranceProperty = this.getHealthInsurancePropertyForMaybeHealthInsuranceInformation(healthInsuranceInformation);
        if (healthInsuranceProperty?.isExcluded) {
            return this.price;
        }
        return healthInsuranceProperty?.coPaymentPrice || 0;
    }
}


export function getConsultationTypesInfo(types: AppointmentConsultationType[], healthInsuranceId: number | null, consultations: ConsultationType[], doctor: DoctorsForMedicalCenter): ConsultationTypeInfo[] {
    const healthInsuranceIsAcceptedByDoctor = doctor.isHealthInsuranceIdAccepted(healthInsuranceId);
    const matchingConsultations = consultations.filter(consultation => types.some(consultationType => consultationType.consultationTypeId === consultation.consultationTypeId));
    return matchingConsultations.map(consultationType => {
        const healthInsuranceProperty = consultationType.getHealthInsurancePropertyForMaybeHealthInsuranceId(healthInsuranceId)
        const isExcluded = healthInsuranceProperty?.isExcluded || false || !healthInsuranceIsAcceptedByDoctor;
        const copay = healthInsuranceProperty?.coPaymentPrice || null;
        return new ConsultationTypeInfo(consultationType.name,
            consultationType.requiresMedicalOrder,
            consultationType.hasInstructions() ? consultationType.instructions || "" : "",
            isExcluded,
            copay,
            healthInsuranceIsAcceptedByDoctor ? 0 : consultationType.price,
            consultationType.acceptsSelfPaidPatient
        );
    });
}


export function getConsultationTypesInfoFromHealthInsuranceInformation(types: AppointmentConsultationType[], healthInsuranceInformation: AppointmentCreationRequestHealthInsuranceInformation | undefined, consultations: ConsultationType[]): ConsultationTypeInfo[] {
    const matchingConsultations = consultations.filter(consultation => types.some(consultationType => consultationType.consultationTypeId === consultation.consultationTypeId));
    return matchingConsultations.map(consultationType => {
        const healthInsuranceProperty = consultationType.getHealthInsurancePropertyForMaybeHealthInsuranceInformation(healthInsuranceInformation || null)
        const isExcluded = healthInsuranceProperty?.isExcluded || false;
        const copay = healthInsuranceProperty?.coPaymentPrice || null;
        return new ConsultationTypeInfo(consultationType.name,
            consultationType.requiresMedicalOrder,
            consultationType.hasInstructions() ? consultationType.instructions || "" : "",
            isExcluded,
            copay,
            consultationType.price || 0,
            consultationType.acceptsSelfPaidPatient
        );
    });
}