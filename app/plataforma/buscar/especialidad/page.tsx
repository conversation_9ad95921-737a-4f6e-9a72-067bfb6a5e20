"use client"

import { useState, useEffect, Suspense, useCallback } from "react"
// Removed <PERSON><PERSON> to let the Topbar replace it on desktop too
import Footer from "@/components/searchresults/Footer"
import SearchResultsTopBar from "@/components/searchresults/SearchResultsTopbar"
import SearchResultsSidebar from "@/components/searchresults/SearchResultsSidebar"
import SearchResultsList, { Result } from "@/components/searchresults/SearchResultsList"
import dynamic from "next/dynamic"
import { useSearchParams, useRouter } from "next/navigation"

// Import the map component dynamically with SSR disabled
const SearchResultsMap = dynamic(
  () => import("@/components/searchresults/SearchResultsMap"),
  { ssr: false }
);

// Create a client component that uses useSearchParams
function SearchResults() {
  const [view, setView] = useState<"list" | "map">("list")
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [noInsurance, setNoInsurance] = useState(false)
  const [coverage, setCoverage] = useState<string>("")
  const [plan, setPlan] = useState<string>("")
  const [timeOfDay, setTimeOfDay] = useState<string>("all")
  const [sortBy, setSortBy] = useState<"date" | "distance">("date")
  const [searchResults, setSearchResults] = useState<Result[]>([])
  const [hoveredResultId, setHoveredResultId] = useState<string | undefined>(undefined)
  const [selectedResultId, setSelectedResultId] = useState<string | undefined>(undefined)
  const searchType = "Especialidad"
  const searchParams = useSearchParams()
  const router = useRouter()
  const searchQuery = searchParams.get("q") || ""
  const locationQuery = searchParams.get("loc") || ""
  const paramsKey = `${searchType}|${searchQuery}|${locationQuery}|${searchParams.get("noCoverage") || ""}|${searchParams.get("coverage") || ""}|${searchParams.get("plan") || ""}|${searchParams.get("time") || ""}`

  // Initialize parameters from URL on page load
  useEffect(() => {
    const noCoverageParam = searchParams.get("noCoverage")
    const coverageParam = searchParams.get("coverage")
    const planParam = searchParams.get("plan")
    const timeParam = searchParams.get("time")
    const sortParam = searchParams.get("sort")
    // We check for zone parameter but don't need to store it
    // as it's handled by the SearchResultsSidebar component

    // Initialize coverage parameters (reset to defaults when absent)
    if (noCoverageParam === "true") {
      setNoInsurance(true)
      setCoverage("")
      setPlan("")
    } else if (coverageParam) {
      setNoInsurance(false)
      setCoverage(coverageParam)
      if (planParam) {
        setPlan(planParam)
      } else {
        setPlan("")
      }
    } else {
      setNoInsurance(false)
      setCoverage("")
      setPlan("")
    }

    // Initialize time parameter (reset when absent)
    if (timeParam) {
      setTimeOfDay(timeParam)
    } else {
      setTimeOfDay("all")
    }

    // Initialize sort parameter (reset when absent)
    if (sortParam === "date" || sortParam === "distance") {
      setSortBy(sortParam)
    } else {
      setSortBy("date")
    }

    // Note: We don't need to initialize location state here
    // because the SearchResultsSidebar component handles that internally
    // and the SearchResultsList component reads the locationQuery directly
  }, [searchParams])

  // Handle time filter changes
  const handleTimeFilterChange = (value: string) => {
    setTimeOfDay(value)
  }

  // Handle sort changes
  const handleSortChange = (value: "date" | "distance") => {
    setSortBy(value)
  }

  // Handle search results updates from the list component using useCallback
  const handleResultsChange = useCallback((results: Result[]) => {
    // Always update when results differ (ensures desktop map stays in sync)
    if (JSON.stringify(results) !== JSON.stringify(searchResults)) {
      setSearchResults(results)
    }
  }, [searchResults]);

  // Get initial results for the map when switching to map view
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined') return;

    if (view === "map" && searchResults.length === 0) {
      // Find the SearchResultsList component and request its current results
      const listComponent = document.querySelector('[data-results-list="true"]');
      if (listComponent) {
        // Trigger a synthetic event to request results
        const event = new CustomEvent('requestResults');
        listComponent.dispatchEvent(event);
      }
    }
  }, [view, searchResults.length]);

  // Handle search in area functionality
  const handleSearchInArea = useCallback(async (bounds: { north: number, south: number, east: number, west: number }) => {
    const centerLat = (bounds.north + bounds.south) / 2;
    const centerLng = (bounds.east + bounds.west) / 2;

    // Try reverse geocoding to get a friendly location within our system
    let resolvedLocation = "";
    try {
      const res = await fetch(`/api/reverse-geocode?lat=${encodeURIComponent(centerLat)}&lon=${encodeURIComponent(centerLng)}`, { cache: 'no-store' });
      if (res.ok) {
        const data = await res.json();
        // Prefer the returned best name
        if (data?.name && typeof data.name === 'string') {
          resolvedLocation = data.name as string;
        }
        // If region is CABA and name is generic, prefer region label
        if (!resolvedLocation && data?.region === 'caba') {
          resolvedLocation = 'Capital Federal';
        }
      }
    } catch {}

    // Fallback to area coordinates if reverse geocode fails
    if (!resolvedLocation) {
      resolvedLocation = `Área ${centerLat.toFixed(3)},${centerLng.toFixed(3)}`;
    }

    let newUrl = `/plataforma/buscar/especialidad?q=${encodeURIComponent(searchQuery)}&loc=${encodeURIComponent(resolvedLocation)}`;

    if (noInsurance) {
      newUrl += "&noCoverage=true";
    } else if (coverage) {
      newUrl += `&coverage=${encodeURIComponent(coverage)}`;
      if (plan) {
        newUrl += `&plan=${encodeURIComponent(plan)}`;
      }
    }
    if (timeOfDay !== "all") {
      newUrl += `&time=${encodeURIComponent(timeOfDay)}`;
    }
    if (sortBy !== "date") {
      newUrl += `&sort=${encodeURIComponent(sortBy)}`;
    }
    const zoneParam = searchParams.get("zone");
    if (zoneParam) {
      newUrl += `&loc=${encodeURIComponent(zoneParam)}`;
    }
    router.push(newUrl);
  }, [searchQuery, noInsurance, coverage, plan, timeOfDay, sortBy, router, searchParams]);

  // Dynamically size the desktop map so its bottom stays flush with the list bottom
  useEffect(() => {
    if (typeof window === 'undefined') return;
    // Helper to find the visible desktop list (ignores the hidden mobile list)
    const findVisibleList = (): HTMLElement | null => {
      const candidates = Array.from(document.querySelectorAll('[data-results-list="true"]')) as HTMLElement[];
      for (const el of candidates) {
        const isVisible = el.offsetParent !== null || el.getClientRects().length > 0;
        if (isVisible) return el;
      }
      return candidates.length ? candidates[candidates.length - 1] : null;
    };

    const adjustDesktopMapHeight = () => {
      const mapEl = document.getElementById('desktop-map-container');
      const listEl = findVisibleList();
      if (!mapEl) return;
      const marginBottom = 30; // leave a tiny gap from the viewport bottom
      const listBottomPadding = 90; // more separation before the list bottom
      const topOffset = mapEl.getBoundingClientRect().top; // distance from viewport top
      const viewportAvailable = Math.max(0, window.innerHeight - topOffset - marginBottom);
      let desired = viewportAvailable;
      if (listEl) {
        const listBottom = listEl.getBoundingClientRect().bottom; // viewport coords
        const untilListBottom = Math.max(0, listBottom - topOffset - listBottomPadding);
        desired = Math.min(viewportAvailable, untilListBottom);
      }
      (mapEl as HTMLElement).style.height = `${desired}px`;
    };

    // Run on mount and on interactions that change layout
    adjustDesktopMapHeight();
    window.addEventListener('scroll', adjustDesktopMapHeight, { passive: true });
    window.addEventListener('resize', adjustDesktopMapHeight);
    const onMapReady = () => adjustDesktopMapHeight();
    window.addEventListener('map-ready', onMapReady);

    // Observe list size changes too
    const listEl = ((): HTMLElement | null => {
      try { return findVisibleList(); } catch { return null; }
    })();
    let listObserver: ResizeObserver | null = null;
    if (listEl && 'ResizeObserver' in window) {
      listObserver = new ResizeObserver(() => adjustDesktopMapHeight());
      try { listObserver.observe(listEl); } catch {}
    }

    // Mobile map container height: fill viewport to footer
    const adjustMobileMapHeight = () => {
      const mobileEl = document.getElementById('mobile-map-container');
      if (!mobileEl) return;
      // Skip dynamic height while fixed full-screen map is active
      if ((mobileEl as HTMLElement).dataset.fixedMap === 'true') return;
      const topOffset = mobileEl.getBoundingClientRect().top;
      const marginBottomMobile = 12;
      const desired = Math.max(0, window.innerHeight - topOffset - marginBottomMobile);
      (mobileEl as HTMLElement).style.height = `${desired}px`;
    };
    adjustMobileMapHeight();
    window.addEventListener('scroll', adjustMobileMapHeight, { passive: true });
    window.addEventListener('resize', adjustMobileMapHeight);
    window.addEventListener('orientationchange', adjustMobileMapHeight);

    return () => {
      window.removeEventListener('scroll', adjustDesktopMapHeight);
      window.removeEventListener('resize', adjustDesktopMapHeight);
      window.removeEventListener('map-ready', onMapReady);
      try { listObserver?.disconnect(); } catch {}
      window.removeEventListener('scroll', adjustMobileMapHeight);
      window.removeEventListener('resize', adjustMobileMapHeight);
      window.removeEventListener('orientationchange', adjustMobileMapHeight);
    };
  }, []);

  // Mobile-only: lock body scroll when viewing the map and ensure container fills viewport
  useEffect(() => {
    if (typeof window === 'undefined') return;
    const isMobile = window.matchMedia('(max-width: 767px)').matches;
    if (!isMobile) return;

    const mobileEl = document.getElementById('mobile-map-container');
    if (view === 'map') {
      // Disable body scroll to prevent rubber-banding on blank space
      document.documentElement.style.overflow = 'hidden';
      document.body.style.overflow = 'hidden';
      document.documentElement.style.height = '100%';
      document.body.style.height = '100%';
      // Pin the map container as a fixed full-height element below the header
      if (mobileEl) {
        const el = mobileEl as HTMLElement;
        el.dataset.fixedMap = 'true';
        const topSafeMargin = 12;
        const topOffset = el.getBoundingClientRect().top + topSafeMargin;
        el.style.position = 'fixed';
        el.style.top = `${topOffset}px`;
        el.style.left = '0';
        el.style.right = '0';
        el.style.bottom = '0';
        el.style.height = 'auto';
        el.style.zIndex = '70';
      }
    } else {
      document.documentElement.style.overflow = '';
      document.body.style.overflow = '';
      document.documentElement.style.height = '';
      document.body.style.height = '';
      if (mobileEl) {
        const el = mobileEl as HTMLElement;
        delete el.dataset.fixedMap;
        el.style.position = '';
        el.style.top = '';
        el.style.left = '';
        el.style.right = '';
        el.style.bottom = '';
        el.style.height = '';
        el.style.zIndex = '';
      }
    }

    return () => {
      document.documentElement.style.overflow = '';
      document.body.style.overflow = '';
      document.documentElement.style.height = '';
      document.body.style.height = '';
    };
  }, [view]);

  return (
    <div className="min-h-screen bg-[#F4F7F9] flex flex-col">
      <style jsx>{`
        #desktop-map-container {
          width: calc((100vw - 2.5rem) * 5/12 - 0.25rem);
          left: calc(50vw - (100vw - 2.5rem)/2 + (100vw - 2.5rem) * 7/12 + 0.25rem);
        }
        @media (min-width: 1300px) {
          #desktop-map-container {
            width: calc(1150px * 5/12 - 0.25rem);
            left: calc(50vw - 650px + 1290px * 7/12 + 0.25rem);
          }
        }
      `}</style>
      {/* Fixed topbar */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-[#F4F7F9]">
        <SearchResultsTopBar
          view={view}
          setView={setView}
          searchType={searchType}
          onOpenSidebar={() => setSidebarOpen(true)}
          searchQuery={searchQuery}
          locationQuery={locationQuery}
          noInsurance={noInsurance}
          setNoInsurance={setNoInsurance}
          coverage={coverage}
          plan={plan}
          onApplyFilters={(coverageData) => {
            setCoverage(coverageData.coverageName);
            setPlan(coverageData.plan);
            setNoInsurance(coverageData.noInsurance);
          }}
          onTimeFilterChange={handleTimeFilterChange}
          onSortChange={handleSortChange}
          sortBy={sortBy}
          hideDesktopViewToggle
        />
      </div>

      {/* Main content area with margin for fixed header */}
      <main className="pt-20 md:pt-32 flex-1">
        <div className="w-full mx-auto max-w-[1300px] px-5 md:px-8 lg:px-12 xl:px-16">
          <div className="pt-4">
            {/* Mobile layout */}
            <div className="grid grid-cols-1 gap-6 md:hidden">
              {/* Mobile sidebar overlay - only visible when sidebar is open on mobile */}
              {sidebarOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 z-[70] md:hidden" onClick={() => setSidebarOpen(false)}></div>
              )}

              {/* Sidebar visible only on mobile; desktop uses popover in topbar */}
              <div className="relative z-auto">
                <SearchResultsSidebar
                  onClose={() => setSidebarOpen(false)}
                  open={sidebarOpen}
                  noInsurance={noInsurance}
                  setNoInsurance={setNoInsurance}
                  onTimeFilterChange={handleTimeFilterChange}
                  onSortChange={handleSortChange}
                  sortBy={sortBy}
                  searchType={searchType}
                  searchQuery={searchQuery}
                  locationQuery={locationQuery}
                  onApplyFilters={(coverageData) => {
                    setCoverage(coverageData.coverageName);
                    setPlan(coverageData.plan);
                    setNoInsurance(coverageData.noInsurance);
                  }}
                />
              </div>

              <div className="-mx-5">
                {/* List or Map depending on mobile toggle */}
                <div className={view === "list" ? "block" : "hidden"}>
                  <SearchResultsList
                    key={paramsKey}
                    noInsurance={noInsurance}
                    searchQuery={searchQuery}
                    searchType={searchType}
                    locationQuery={locationQuery}
                    coverage={coverage}
                    plan={plan}
                    timeOfDay={timeOfDay}
                    sortBy={sortBy}
                    onResultsChange={handleResultsChange}
                    onItemHover={(id) => setHoveredResultId(id)}
                    onItemLeave={() => setHoveredResultId(undefined)}
                    highlightedResultId={selectedResultId}
                  />
                </div>

                {view === "map" && (
                  <div className="relative z-20" id="mobile-map-container">
                    <SearchResultsMap
                      key={paramsKey}
                      results={searchResults}
                      searchType={searchType}
                      searchQuery={searchQuery}
                      noInsurance={noInsurance}
                      coverage={coverage}
                      plan={plan}
                      timeOfDay={timeOfDay}
                      hoveredResultId={hoveredResultId}
                      selectedResultId={selectedResultId}
                      onMarkerClick={(id) => {
                        setSelectedResultId(id);
                        const el = document.getElementById(`result-${id}`);
                        if (el) {
                          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                      }}
                      onUnselect={() => setSelectedResultId(undefined)}
                      onSearchInArea={handleSearchInArea}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Desktop split layout */}
            <div className="hidden md:grid md:grid-cols-12 md:gap-2">
              <div className="md:col-span-7">
                <SearchResultsList
                  key={paramsKey + "|desktop"}
                  noInsurance={noInsurance}
                  searchQuery={searchQuery}
                  searchType={searchType}
                  locationQuery={locationQuery}
                  coverage={coverage}
                  plan={plan}
                  timeOfDay={timeOfDay}
                  sortBy={sortBy}
                  onResultsChange={handleResultsChange}
                  onItemHover={(id) => setHoveredResultId(id)}
                  onItemLeave={() => setHoveredResultId(undefined)}
                  highlightedResultId={selectedResultId}
                />
              </div>
              <div className="md:col-span-5">
                {/* Map with fixed positioning that stays in place */}
              <div className="fixed top-34 z-30 h-[calc(100vh-8rem)] md:h-auto" id="desktop-map-container">
                  <SearchResultsMap
                    key={paramsKey + "|desktop"}
                    results={searchResults}
                    searchType={searchType}
                    searchQuery={searchQuery}
                    noInsurance={noInsurance}
                    coverage={coverage}
                    plan={plan}
                    timeOfDay={timeOfDay}
                    hoveredResultId={hoveredResultId}
                    selectedResultId={selectedResultId}
                    onMarkerClick={(id) => {
                      setSelectedResultId(id);
                      const el = document.getElementById(`result-${id}`);
                      if (el) {
                        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                      }
                    }}
                    onUnselect={() => setSelectedResultId(undefined)}
                    onSearchInArea={handleSearchInArea}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}

// Main page component with Suspense wrapper
export default function SearchResultsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex flex-col items-center justify-center">
        <p>Cargando resultados...</p>
      </div>
    }>
      <SearchResults />
    </Suspense>
  )
}

