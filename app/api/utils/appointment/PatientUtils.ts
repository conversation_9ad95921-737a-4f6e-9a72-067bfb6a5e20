"use client"

import {PatientCreationRequestFromMedicalCenter} from "@/app/api/requestBodies/PatientCreationRequestFromMedicalCenter";

export async function createPatientFromMedicalCenter(
    patientData: PatientCreationRequestFromMedicalCenter,
    employeeUserId: number,
    medicalCenterId: number,
    jwtToken: string | null
): Promise<number> {
    try {
        const response = await fetch(`/api/patients/create-from-medical-center?medicalCenterId=${encodeURIComponent(medicalCenterId)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${jwtToken}`,
                'x-user-id': employeeUserId.toString(),
            },
            body: JSON.stringify(patientData),
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to create patient: ${response.status}`);
        }

        // Response is the patient ID as an integer
        const patientId: number = await response.json();
        return patientId;
    } catch (error) {
        console.error('Error creating patient from medical center:', error);
        throw error;
    }
}
