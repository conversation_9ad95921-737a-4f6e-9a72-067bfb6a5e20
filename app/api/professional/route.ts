import { NextRequest, NextResponse } from 'next/server';

export interface CreateProfessionalRequest {
    name: string;
    surname: string;
    identificationNumber?: string;
    medicalLicense?: string;
    createdBy: number;
}

export interface CreateProfessionalResponse {
    id: number;
}

export async function POST(request: NextRequest) {
    try {
        const body: CreateProfessionalRequest = await request.json();

        // Validate required fields
        const requiredFields: (keyof CreateProfessionalRequest)[] = [
            'name',
            'surname',
            'createdBy'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null || body[field] === '') {
                return NextResponse.json(
                    { error: `Missing required field: ${field}` },
                    { status: 400 }
                );
            }
        }

        // Validate name and surname are not empty strings
        if (body.name.trim().length === 0) {
            return NextResponse.json(
                { error: 'Name cannot be empty' },
                { status: 400 }
            );
        }

        if (body.surname.trim().length === 0) {
            return NextResponse.json(
                { error: 'Surname cannot be empty' },
                { status: 400 }
            );
        }

        // Validate createdBy is a positive number
        if (body.createdBy <= 0) {
            return NextResponse.json(
                { error: 'CreatedBy must be a positive number' },
                { status: 400 }
            );
        }

        // Prepare the request body for the external API
        const requestBody = {
            name: body.name.trim(),
            surname: body.surname.trim(),
            identificationNumber: body.identificationNumber || "",
            medicalLicense: body.medicalLicense || "",
            createdBy: body.createdBy
        };

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/professional`,
            {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to create professional',
                    details: errorText,
                    status: response.status
                },
                { status: response.status }
            );
        }

        const result = await response.json();
        return NextResponse.json(result, { status: 200 });

    } catch (error) {
        console.error('Error creating professional:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
