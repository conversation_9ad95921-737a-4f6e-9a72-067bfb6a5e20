import { NextRequest, NextResponse } from 'next/server';

export interface ProfessionalDetailResponse {
    personalInformation: {
        name: string;
        surname: string;
        email: string;
        matriculaNacional?: string;
    };
    specialties: string[];
    healthInsurances: Array<{
        name: string;
        plans: Array<{
            id: number;
            name: string;
            isAccepted: boolean;
            accepted: boolean;
        }>;
    }>;
    schedules: {
        appointmentSchedules: Array<{
            day: string;
            startingAt: string;
            endingAt: string;
            startTime: string;
            endTime: string;
            weeklyFrequency: number;
        }>;
        specialSchedules: Array<{
            date: string;
            startTime: string;
            endTime: string;
        }>;
        vacationSchedules: Array<{
            fromDate: string;
            toDate: string;
        }>;
    };
    consultationTypes: Array<{
        consultationTypeId: number;
        name: string;
        price: number;
        availableOnline: boolean;
        acceptsSelfPaidPatient: boolean;
        requiresMedicalOrder: boolean;
        appointmentIntervalAmount: number;
        instructions: string;
        dailyLimit: number;
        healthInsuranceExtraProperties: Array<{
            healthInsuranceId: number;
            name: string;
            plan: string;
            isExcluded: boolean;
            coPaymentPrice: number;
            excluded: boolean;
        }>;
    }>;
    bookingPoliciesDTO: {
        maximumAnticipationAppointmentTimeLimit: string;
        minimumAnticipationAppointmentTimeLimit: string;
        overlappedAppointmentLimit: string;
    };
}

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const email = searchParams.get('email');

        if (!email) {
            return NextResponse.json(
                { error: 'Email parameter is required' },
                { status: 400 }
            );
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return NextResponse.json(
                { error: 'Invalid email format' },
                { status: 400 }
            );
        }

        // Make request to external API to get professional details
        const response = await fetch(
            `${process.env.BACKEND_URL}/professional/detail?email=${encodeURIComponent(email)}`,
            {
                method: 'GET',
                headers: {
                    'accept': '*/*',
                },
            }
        );

        if (response.status === 404) {
            return NextResponse.json(
                { error: 'Professional not found' },
                { status: 404 }
            );
        }

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to get professional details',
                    details: errorText,
                    status: response.status
                },
                { status: response.status }
            );
        }

        const result: ProfessionalDetailResponse = await response.json();
        return NextResponse.json(result, { status: 200 });

    } catch (error) {
        console.error('Error getting professional details:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
