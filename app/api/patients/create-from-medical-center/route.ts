import {NextRequest, NextResponse} from 'next/server'
import {cookies} from 'next/headers'
import {PatientCreationRequestFromMedicalCenter} from '@/app/api/requestBodies/PatientCreationRequestFromMedicalCenter'

export async function POST(request: NextRequest) {
    try {
        const cookieStore = await cookies()
        const tokenFromCookie = cookieStore.get('auth0_access_token')?.value || cookieStore.get('auth0_jwt')?.value
        const authHeader = request.headers.get('authorization') || ''
        const tokenFromHeader = authHeader.toLowerCase().startsWith('bearer ') ? authHeader.slice(7) : undefined
        const token = tokenFromHeader || tokenFromCookie

        if (!token) {
            return NextResponse.json({error: 'Not authenticated'}, {status: 401})
        }

        const payload: PatientCreationRequestFromMedicalCenter = await request.json()

        const url = new URL(request.url)
        const medicalCenterId = url.searchParams.get('medicalCenterId')

        if (!medicalCenterId) {
            return NextResponse.json({error: 'Missing medical center ID'}, {status: 400})
        }

        const userId = request.headers.get('x-user-id') || undefined

        if (!userId) {
            return NextResponse.json({error: 'Missing user identifier'}, {status: 400})
        }

        const upstreamRes = await fetch(
            `${process.env.BACKEND_URL}/patients/create-from-medical-center/${encodeURIComponent(medicalCenterId)}?userId=${encodeURIComponent(userId)}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify(payload),
            }
        )

        if (!upstreamRes.ok) {
            const errorText = await upstreamRes.text()
            return NextResponse.json(
                {error: errorText || `Backend error: ${upstreamRes.status}`},
                {status: upstreamRes.status}
            )
        }

        const patientId = parseInt(await upstreamRes.text())

        if (isNaN(patientId)) {
            return NextResponse.json(
                {error: 'Invalid response from backend'},
                {status: 500}
            )
        }

        return NextResponse.json(patientId, {status: 201})
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Upstream error'
        return NextResponse.json({error: errorMessage}, {status: 500})
    }
}
