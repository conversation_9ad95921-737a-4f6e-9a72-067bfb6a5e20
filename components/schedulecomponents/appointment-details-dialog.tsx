"use client"

import {use<PERSON><PERSON>back, useEffect, useMemo, useState} from "react"
import {<PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
import {Textarea} from "@/components/ui/textarea"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {AlertCircle, AlertTriangle, Info} from "lucide-react"
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";

interface AppointmentDetailsDialogProps {
    appointment: ProfessionalAppointment | null
    isOpen: boolean
    onClose: () => void
    doctorInfo: DoctorsForMedicalCenter
}

export function AppointmentDetailsDialog({
                                             appointment,
                                             isOpen,
                                             onClose,
                                             doctorInfo
                                         }: AppointmentDetailsDialogProps) {
    const [patientAppointments, setPatientAppointments] = useState<ProfessionalAppointment[]>([])
    const [showUpPercentage, setShowUpPercentage] = useState(0)

    // State for consultation info dialog
    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false)
    const [consultationTypesInfo, setConsultationTypesInfo] = useState<ConsultationTypeInfo[]>([])
    const [selectedCoverage, setSelectedCoverage] = useState("")
    const consultationTypes = useMemo(() => doctorInfo.consultationTypes, [doctorInfo.consultationTypes]);

    const showConsultationInfo = useCallback((appointment: ProfessionalAppointment) => {
        if (appointment.hasInstructionInfo(consultationTypes)) {
            const typesInfo = appointment.getConsultationTypeInfo(consultationTypes, doctorInfo);
            setSelectedCoverage(appointment.healthInsuranceShowableString());
            setConsultationTypesInfo(typesInfo);
            setShowInstructionsDialog(true);
        }
    }, [consultationTypes]);

    useEffect(() => {
        if (appointment) {
            // Note: appointments and getPatientById should come from context or props
            // For now, using the appointment data directly
            setPatientAppointments([appointment]) // Simplified for now

            // Use the attendance percentage from the appointment object
            setShowUpPercentage(appointment.attendancePercentage || 0)
        }
    }, [appointment])

    if (!appointment) return null

    const appointmentDate = new Date(`${appointment.date}T${appointment.startTime}:00`).toLocaleDateString("es-ES", {
        weekday: "long",
        day: "numeric",
        month: "long",
        year: "numeric",
    })

    const patientName = appointment.patientName
    const patientDni = appointment.identificationNumber


    const doctorName = doctorInfo.name
    const doctorSpecialties = doctorInfo.specialties.join(", ")

    return (
        <>
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[800px] max-h-[70vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Detalles de turno</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-6">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium mb-2">Nombre Completo:</h3>
                                    <p>{patientName}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">DNI:</h3>
                                    <p>{patientDni}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Contacto:</h3>
                                    <p>{appointment.phone}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Cobertura:</h3>
                                    <p>{appointment.healthInsuranceShowableString()}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Estado del turno:</h3>
                                    <p>{appointment.getStateAsSpanishString()}</p>
                                </div>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium mb-2">Fecha y hora del turno:</h3>
                                    <p>{appointmentDate} - {appointment.startTime}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Atención:</h3>
                                    <div className="flex items-center">
                                        <p>{appointment.getConsultationTypesAsString() || "N/A"}</p>
                                        {appointment.hasInstructionInfo(consultationTypes) && (
                                            <div
                                                className="ml-2 cursor-pointer"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    showConsultationInfo(appointment);
                                                }}
                                                aria-label={`Ver información de tipos de consulta`}
                                                role="button"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter' || e.key === ' ') {
                                                        e.stopPropagation();
                                                        showConsultationInfo(appointment);
                                                    }
                                                }}
                                            >
                                                <Info className="h-4 w-4 text-blue-500 hover:text-blue-700"/>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Profesional:</h3>
                                    <p>{doctorName} ({doctorSpecialties})</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Atenciones en tu establecimiento:</h3>
                                    <p>{patientAppointments.length} turnos</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Porcentaje de Asistencia:</h3>
                                    <p>{showUpPercentage.toFixed(0)}%</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 className="font-medium mb-2">Detalles Adicionales</h3>
                            <Textarea
                                placeholder="Lo escrito por el paciente en 'aclaraciones' desde turnera aparecerá aquí."
                                className="min-h-[100px]"
                                readOnly
                            />
                        </div>
                        <Button onClick={onClose} className="w-full bg-blue-500 hover:bg-blue-600">
                            Cerrar
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Instructions Dialog */}
            <AlertDialog open={showInstructionsDialog} onOpenChange={setShowInstructionsDialog}>
                <AlertDialogContent
                    className={`border border-gray-300 shadow-lg ${consultationTypesInfo.length > 1 ? "max-w-3xl" : "max-w-md"}`}>
                    <AlertDialogHeader className="pb-2 border-b border-gray-200">
                        <AlertDialogTitle className="text-center text-lg font-semibold text-gray-800">Información
                            importante</AlertDialogTitle>
                    </AlertDialogHeader>

                    {/* Content for multiple consultation types */}
                    <div className="mt-4 space-y-4">
                        {consultationTypesInfo.map((consultationInfo, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                                <h4 className="font-semibold text-gray-800 text-lg">{consultationInfo.name}</h4>

                                {consultationInfo.isExcluded && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                        <p className="text-red-700 flex items-center gap-2">
                                            <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0"/>
                                            <span>
                                                La atención <span
                                                className="font-semibold">{consultationInfo.name}</span> no es cubierta por el profesional con la cobertura <span
                                                className="font-semibold">{selectedCoverage}</span>.
                                            </span>
                                        </p>
                                    </div>
                                )}

                                {consultationInfo.requiresMedicalOrder && (
                                    <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                        <p className="text-amber-700 flex items-center gap-2">
                                            <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0"/>
                                            <span>Esta atención requiere orden médica.</span>
                                        </p>
                                    </div>
                                )}

                                {consultationInfo.copayAmount !== null && (
                                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <p className="text-blue-700 flex items-center gap-2">
                                            <Info className="h-5 w-5 text-blue-500 flex-shrink-0"/>
                                            <span>
                                                Copago por <span
                                                className="font-semibold">{consultationInfo.name}</span> con plan <span
                                                className="font-semibold">{selectedCoverage}</span>: <span
                                                className="font-semibold">${consultationInfo.copayAmount}</span>
                                            </span>
                                        </p>
                                    </div>
                                )}

                                {consultationInfo.instructions && (
                                    <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div className="text-gray-700">
                                            <p className="font-medium mb-2">Indicaciones para atención <span
                                                className="font-semibold">{consultationInfo.name}</span>:</p>
                                            <p className="whitespace-pre-line">
                                                {consultationInfo.instructions}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    <div className="flex justify-center mt-6">
                        <AlertDialogAction
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors">
                            Entendido
                        </AlertDialogAction>
                    </div>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}