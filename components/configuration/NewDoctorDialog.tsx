"use client"

import {use<PERSON>ontext, useEffect, useRef, useState} from "react"
import {NewDoctorContext} from "@/contexts/NewDoctorContext"
import {Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle} from "@/components/ui/dialog"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Button} from "@/components/ui/button"
import {Badge} from "@/components/ui/badge"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Plus, Search, X, UserPlus, Check, Stethoscope, ChevronLeft, ChevronRight, User, Mail, Shield} from "lucide-react"
import {SPECIALTIES, Specialty} from "@/data/specialties"
import {toast} from "sonner"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import type { CreateProfessionalRequest, CreateProfessionalResponse } from "@/app/api/professional/route"
import type { CreateProfessionalMedicalCenterRelationshipRequest, CreateProfessionalMedicalCenterRelationshipResponse } from "@/app/api/professional-medical-center-relationship/route"
import type { CreateSpecialtyProfessionalMedicalCenterRelationshipRequest, CreateSpecialtyProfessionalMedicalCenterRelationshipResponse } from "@/app/api/specialty-professional-medical-center-relationship/route"

// Shared styles aligned with platform/landing pages
const inputClassNames =
    'rounded-xl border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 shadow-inner focus:border-blue-400 focus:ring-2 focus:ring-blue-100';
const labelClassNames = 'text-slate-700 text-sm font-medium';

type Step = 1 | 2;

// Helper function to capitalize names properly
const capitalizeName = (name: string) => {
    return name
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
}

// Helper function to get surname initial
const getSurnameInitial = (surname: string) => {
    return surname.charAt(0).toUpperCase() + ".";
}

export default function NewDoctorDialog() {
    const {
        isNewDoctorDialogOpen,
        setIsNewDoctorDialogOpen,
        newDoctor,
        setNewDoctor,
        removeSpecialty,
    } = useContext(NewDoctorContext)

    const {activeMedicalCenterId} = useContext(MedicalCenterContext)

    const [currentStep, setCurrentStep] = useState<Step>(1)
    const [emailSearched, setEmailSearched] = useState(false)
    const [searchResult, setSearchResult] = useState<"found" | "not-found" | null>(null)
    const [existingDoctorData, setExistingDoctorData] = useState<{
        name: string;
        surname: string;
        email: string;
        specialties?: string[];
    } | null>(null)
    const [specialtySearch, setSpecialtySearch] = useState("")
    const [isProcessing, setIsProcessing] = useState(false)
    const [isSearching, setIsSearching] = useState(false)

    // Search for existing doctor by email
    const handleSearchByEmail = async () => {
        if (!newDoctor.email.trim()) {
            toast.error('Por favor ingrese el email del profesional');
            return;
        }

        const email = newDoctor.email.trim();
        if (!email.includes('@')) {
            toast.error('Por favor ingrese un email válido');
            return;
        }

        setIsSearching(true);
        setEmailSearched(true);

        try {
            // Search for existing professional by email
            const response = await fetch(`/api/professional/detail?email=${encodeURIComponent(email)}`);
            
            if (response.ok) {
                const doctorData = await response.json();
                setExistingDoctorData({
                    name: doctorData.personalInformation.name,
                    surname: doctorData.personalInformation.surname || '',
                    email: doctorData.personalInformation.email,
                    specialties: doctorData.specialties || []
                });
                setSearchResult("found");
                toast.success('Profesional encontrado');
            } else if (response.status === 404) {
                setExistingDoctorData(null);
                setSearchResult("not-found");
                toast.info('Profesional no encontrado. Complete los datos para crearlo.');
            } else {
                throw new Error('Error en la búsqueda');
            }
        } catch (error) {
            console.error('Error searching for doctor:', error);
            toast.error('Error al buscar el profesional');
            setSearchResult("not-found");
        } finally {
            setIsSearching(false);
        }
    }

    const handleEmailChange = (value: string) => {
        setNewDoctor({...newDoctor, email: value})
        setEmailSearched(false)
        setSearchResult(null)
        setExistingDoctorData(null)
        setSpecialtySearch("")
    }

    useEffect(() => {
        if (isNewDoctorDialogOpen) {
            // Reset all dialog state when opening
            setNewDoctor({
                firstName: "",
                lastName: "",
                email: "",
                specialties: [] as Specialty[],
            })
            setCurrentStep(1)
            setEmailSearched(false)
            setSearchResult(null)
            setExistingDoctorData(null)
            setSpecialtySearch("")
            setIsProcessing(false)
            setIsSearching(false)
        }
    }, [isNewDoctorDialogOpen, setNewDoctor])

    const handleSpecialtyToggle = (specialty: string) => {
        const isSelected = newDoctor.specialties.includes(specialty as Specialty)
        if (isSelected) {
            // Remove specialty
            setNewDoctor({
                ...newDoctor,
                specialties: newDoctor.specialties.filter(s => s !== specialty)
            })
        } else {
            // Add specialty
            setNewDoctor({
                ...newDoctor,
                specialties: [...newDoctor.specialties, specialty as Specialty]
            })
        }
    }

    // Filter specialties based on search
    const filteredSpecialties = SPECIALTIES.filter(specialty =>
        specialty.toLowerCase().includes(specialtySearch.toLowerCase())
    )

    if (!newDoctor) {
        return null
    }

    // Validation helpers
    const isEmailValid = (): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return newDoctor.email.trim().length > 0 && emailRegex.test(newDoctor.email);
    };
    const isNameValid = (): boolean => newDoctor.firstName.trim().length >= 2;
    const isLastNameValid = (): boolean => newDoctor.lastName.trim().length >= 2;
    const isSpecialtiesValid = (): boolean => newDoctor.specialties.length > 0;

    // Step validation
    const isStepValid = (step: Step): boolean => {
        switch (step) {
            case 1:
                return emailSearched && (
                    searchResult === "found" || 
                    (searchResult === "not-found" && isNameValid() && isLastNameValid())
                );
            case 2:
                return isSpecialtiesValid();
            default:
                return false;
        }
    };

    const validateStep = (step: Step): boolean => {
        switch (step) {
            case 1:
                if (!emailSearched) {
                    toast.error('Por favor busque por email primero');
                    return false;
                }
                if (searchResult === "not-found" && !isNameValid()) {
                    toast.error('El nombre es requerido (mínimo 2 caracteres)');
                    return false;
                }
                if (searchResult === "not-found" && !isLastNameValid()) {
                    toast.error('El apellido es requerido (mínimo 2 caracteres)');
                    return false;
                }
                return true;
            case 2:
                if (!isSpecialtiesValid()) {
                    toast.error('Debe seleccionar al menos una especialidad');
                    return false;
                }
                return true;
            default:
                return false;
        }
    };

    const handleNext = () => {
        if (validateStep(currentStep)) {
            setCurrentStep(2);
        }
    };

    const handlePrevious = () => {
        setCurrentStep(1);
    };

    const handleClose = () => {
        if (!isProcessing) {
            setCurrentStep(1);
            setIsNewDoctorDialogOpen(false);
        }
    };

    const handleCreateNewDoctor = async () => {
        if (!activeMedicalCenterId || isProcessing) {
            return;
        }

        setIsProcessing(true);
        console.log("Processing doctor addition to medical center...");

        try {
            if (searchResult === "found" && existingDoctorData) {
                // Existing doctor - just add to medical center
                console.log("Adding existing doctor to medical center");
                
                const relationshipRequest: CreateProfessionalMedicalCenterRelationshipRequest = {
                    email: existingDoctorData.email,
                    overlappedAppointmentLimit: "ONE_PER_ONE",
                    maximumAnticipationAppointmentTimeLimit: "60", // 60 days
                    minimumAnticipationAppointmentTimeLimit: "2", // 2 hours
                    appointmentIntervalTime: "15", // 15 minutes
                    medicalCenterId: Number(activeMedicalCenterId),
                    createdById: 1, // TODO: Get actual user ID from auth context
                };

                const relationshipResponse = await fetch('/api/professional-medical-center-relationship', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(relationshipRequest),
                });

                if (!relationshipResponse.ok) {
                    const errorData = await relationshipResponse.json();
                    throw new Error(errorData.error || 'Failed to add professional to medical center');
                }

                // Add specialties if they differ from existing ones
                if (newDoctor.specialties.length > 0) {
                    // TODO: Implement specialty relationship creation for existing doctor
                    console.log("Adding specialties for existing doctor");
                }

                toast.success('Profesional agregado al establecimiento exitosamente');
            } else {
                // New doctor - create professional first
                console.log("Creating new professional");
                
                const professionalRequest: CreateProfessionalRequest = {
                    name: capitalizeName(newDoctor.firstName.trim()),
                    surname: capitalizeName(newDoctor.lastName.trim()),
                    identificationNumber: "", // Will be filled by doctor later
                    medicalLicense: "", // Will be filled by doctor later
                    createdBy: 1 // TODO: Get actual user ID from auth context
                };

                const professionalResponse = await fetch('/api/professional', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(professionalRequest),
                });

                if (!professionalResponse.ok) {
                    const errorData = await professionalResponse.json();
                    throw new Error(errorData.error || 'Failed to create professional');
                }

                const professionalResult: CreateProfessionalResponse = await professionalResponse.json();
                const professionalId = professionalResult.id;

                // Add to medical center
                const relationshipRequest: CreateProfessionalMedicalCenterRelationshipRequest = {
                    email: newDoctor.email,
                    overlappedAppointmentLimit: "ONE_PER_ONE",
                    maximumAnticipationAppointmentTimeLimit: "60",
                    minimumAnticipationAppointmentTimeLimit: "2",
                    appointmentIntervalTime: "15",
                    medicalCenterId: Number(activeMedicalCenterId),
                    createdById: 1,
                };

                const relationshipResponse = await fetch('/api/professional-medical-center-relationship', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(relationshipRequest),
                });

                if (!relationshipResponse.ok) {
                    const errorData = await relationshipResponse.json();
                    throw new Error(errorData.error || 'Failed to add professional to medical center');
                }

                // Add specialties
                if (newDoctor.specialties.length > 0) {
                    const specialtyPromises = newDoctor.specialties.map(async (specialty) => {
                        const specialtyRequest: CreateSpecialtyProfessionalMedicalCenterRelationshipRequest = {
                            specialtyName: specialty,
                            professionalId: professionalId,
                            medicalCenterId: Number(activeMedicalCenterId),
                            createdById: 1
                        };

                        const specialtyResponse = await fetch('/api/specialty-professional-medical-center-relationship', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(specialtyRequest),
                        });

                        if (!specialtyResponse.ok) {
                            const errorData = await specialtyResponse.json();
                            console.error(`Failed to create specialty relationship for ${specialty}:`, errorData);
                            return { specialty, success: false, error: errorData.error };
                        }

                        const specialtyResult: CreateSpecialtyProfessionalMedicalCenterRelationshipResponse = await specialtyResponse.json();
                        return { specialty, success: true, result: specialtyResult };
                    });

                    const specialtyResults = await Promise.all(specialtyPromises);
                    const failedSpecialties = specialtyResults.filter(r => !r.success);

                    if (failedSpecialties.length > 0) {
                        console.warn(`Failed to create ${failedSpecialties.length} specialty relationships:`, failedSpecialties);
                        toast.warning(`Profesional creado pero ${failedSpecialties.length} especialidades no se pudieron asociar`);
                    }
                }

                toast.success('Profesional creado y agregado al establecimiento exitosamente');
            }

            // Reset form and close dialog
            setNewDoctor({
                firstName: "",
                lastName: "",
                email: "",
                specialties: [] as Specialty[],
            });
            setCurrentStep(1);
            setEmailSearched(false);
            setSearchResult(null);
            setExistingDoctorData(null);
            setSpecialtySearch("");

            // Dispatch success event
            if (typeof window !== 'undefined') {
                const savedEvent = new CustomEvent('doctor-config-saved', {
                    detail: {
                        medicalCenterId: activeMedicalCenterId,
                        timestamp: Date.now()
                    }
                });
                window.dispatchEvent(savedEvent);

                // Show loading overlay and refresh
                const loadingEvent = new CustomEvent('show-loading-overlay');
                window.dispatchEvent(loadingEvent);

                setTimeout(() => {
                    window.location.reload();
                }, 100);
            }

            setIsNewDoctorDialogOpen(false);

        } catch (error) {
            console.error("Error adding professional to medical center:", error);
            toast.error(error instanceof Error ? error.message : 'Error al agregar el profesional');
        } finally {
            setIsProcessing(false);
        }
    };

    // Step Components
    const renderStep1 = () => (
        <div className="space-y-4 md:space-y-6">
            <div className="text-center space-y-2">
                <div className="flex items-center justify-center gap-2 md:gap-3">
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/20 border border-blue-300">
                        <Shield className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />
                    </div>
                    <h3 className="text-lg md:text-xl font-semibold text-slate-700">Agregar Profesional</h3>
                </div>
                <p className="text-sm md:text-base text-slate-500">Busque por email o cree un nuevo profesional.</p>
            </div>

            <div className="space-y-4 md:space-y-6 max-w-2xl mx-auto">
                {/* Email Search Section */}
                <div className="space-y-4">
                    <div className="flex gap-3 sm:gap-4 items-end">
                        <div className="flex-1 space-y-2">
                            <Label htmlFor="email" className={`${labelClassNames} flex items-center gap-2`}>
                                <Mail className="w-4 h-4" />
                                Email del Profesional
                                <span className="text-red-500">*</span>
                                {isEmailValid() && <Check className="w-4 h-4 text-blue-600" />}
                            </Label>
                            <Input
                                id="email"
                                type="email"
                                value={newDoctor.email}
                                onChange={(e) => handleEmailChange(e.target.value)}
                                placeholder="Ej: <EMAIL>"
                                className={`${inputClassNames} ${isEmailValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                            />
                        </div>
                        <Button
                            type="button"
                            onClick={handleSearchByEmail}
                            disabled={!isEmailValid() || isSearching}
                            className="rounded-xl bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 sm:py-2 whitespace-nowrap disabled:opacity-50"
                        >
                            {isSearching ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                                <>
                                    <Search className="h-4 w-4 mr-2"/>
                                    Buscar
                                </>
                            )}
                        </Button>
                    </div>

                    {/* Search Results */}
                    {searchResult === "found" && existingDoctorData && (
                        <div className="bg-green-50 border border-green-200 text-green-700 p-4 rounded-xl">
                            <div className="flex items-start gap-3">
                                <User className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                                <div className="space-y-1">
                                    <p className="font-medium">Profesional encontrado</p>
                                    <p className="text-sm">
                                        <span className="font-medium">{existingDoctorData.name}</span>{" "}
                                        <span className="font-medium">{getSurnameInitial(existingDoctorData.surname)}</span>
                                    </p>
                                    <p className="text-xs text-green-600">
                                        Solo se muestra información mínima por seguridad y privacidad
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}
                    
                    {searchResult === "not-found" && (
                        <div className="bg-amber-50 border border-amber-200 text-amber-700 p-4 rounded-xl">
                            <div className="flex items-start gap-3">
                                <UserPlus className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                                <div className="space-y-1">
                                    <p className="font-medium">Profesional no encontrado</p>
                                    <p className="text-sm">Complete los datos para crear un nuevo profesional</p>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* New Doctor Form - Only show if not found */}
                {searchResult === "not-found" && (
                    <div className="space-y-4 pt-4 border-t border-slate-200">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="firstName" className={`${labelClassNames} flex items-center gap-2`}>
                                    Nombre
                                    <span className="text-red-500">*</span>
                                    {isNameValid() && <Check className="w-4 h-4 text-blue-600" />}
                                </Label>
                                <Input
                                    id="firstName"
                                    value={newDoctor.firstName || ""}
                                    onChange={(e) => setNewDoctor({
                                        ...newDoctor,
                                        firstName: capitalizeName(e.target.value)
                                    })}
                                    placeholder="Ej: Juan"
                                    className={`${inputClassNames} ${isNameValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="lastName" className={`${labelClassNames} flex items-center gap-2`}>
                                    Apellido
                                    <span className="text-red-500">*</span>
                                    {isLastNameValid() && <Check className="w-4 h-4 text-blue-600" />}
                                </Label>
                                <Input
                                    id="lastName"
                                    value={newDoctor.lastName || ""}
                                    onChange={(e) => setNewDoctor({...newDoctor, lastName: capitalizeName(e.target.value)})}
                                    placeholder="Ej: Pérez"
                                    className={`${inputClassNames} ${isLastNameValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );

    const renderStep2 = () => (
        <div className="space-y-4 md:space-y-6">
            <div className="text-center space-y-2">
                <div className="flex items-center justify-center gap-2 md:gap-3">
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/20 border border-blue-300">
                        <Stethoscope className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />
                    </div>
                    <h3 className="text-lg md:text-xl font-semibold text-slate-700">Especialidades</h3>
                </div>
                <p className="text-sm md:text-base text-slate-500">Seleccione las especialidades que ejercerá en este establecimiento.</p>
            </div>

            <div className="space-y-4 md:space-y-6 max-w-4xl mx-auto">
                {/* Search Input */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <Input
                        placeholder="Buscar especialidades..."
                        value={specialtySearch}
                        onChange={(e) => setSpecialtySearch(e.target.value)}
                        className="pl-10 pr-10 rounded-xl border-slate-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100"
                    />
                    {specialtySearch && (
                        <button
                            onClick={() => setSpecialtySearch("")}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 hover:text-slate-600 transition-colors"
                        >
                            <X className="h-4 w-4" />
                        </button>
                    )}
                </div>

                {/* Specialties Selection Area */}
                <div className="bg-white border border-slate-200 rounded-xl shadow-sm overflow-hidden">
                    <div className="max-h-48 md:max-h-64 overflow-y-auto">
                        {filteredSpecialties.length === 0 ? (
                            <div className="text-center py-6 md:py-8 text-slate-500">
                                <Search className="h-6 w-6 md:h-8 md:w-8 mx-auto mb-2 opacity-50" />
                                <p className="text-sm md:text-base">No se encontraron especialidades</p>
                                <p className="text-xs md:text-sm">Intente con otros términos de búsqueda</p>
                            </div>
                        ) : (
                            <div className="p-3 md:p-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {filteredSpecialties.map((specialty) => {
                                        const isSelected = newDoctor.specialties.includes(specialty)
                                        return (
                                            <div
                                                key={specialty}
                                                onClick={() => handleSpecialtyToggle(specialty)}
                                                className={`group relative flex items-center p-2.5 md:p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                                                    isSelected
                                                        ? 'bg-blue-50 border-blue-300 hover:bg-blue-100'
                                                        : 'bg-white border-slate-200 hover:border-slate-300 hover:bg-slate-50'
                                                }`}
                                            >
                                                <div className={`flex-shrink-0 w-4 h-4 md:w-5 md:h-5 rounded border-2 flex items-center justify-center mr-2 md:mr-3 transition-colors ${
                                                    isSelected
                                                        ? 'bg-blue-600 border-blue-600'
                                                        : 'border-slate-300 group-hover:border-slate-400'
                                                }`}>
                                                    {isSelected && (
                                                        <Check className="h-2.5 w-2.5 md:h-3 md:w-3 text-white" />
                                                    )}
                                                </div>
                                                <span className={`text-sm font-medium ${
                                                    isSelected ? 'text-blue-900' : 'text-slate-700'
                                                }`}>
                                                    {specialty}
                                                </span>
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Selected Specialties Summary */}
                {newDoctor.specialties.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-3 md:p-4">
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="text-xs md:text-sm font-medium text-blue-900 flex items-center gap-2">
                                Especialidades Seleccionadas ({newDoctor.specialties.length})
                                {isSpecialtiesValid() && <Check className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />}
                            </h4>
                            <Button
                                onClick={() => setNewDoctor({...newDoctor, specialties: []})}
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 text-xs md:text-sm"
                            >
                                <X className="h-3 w-3 mr-1" />
                                Limpiar todas
                            </Button>
                        </div>
                        <div className="flex flex-wrap gap-1.5 md:gap-2">
                            {newDoctor.specialties.map((specialty) => (
                                <Badge
                                    key={specialty}
                                    className="px-2 md:px-3 py-1 md:py-1.5 bg-white text-blue-700 hover:bg-blue-50 border border-blue-200 rounded-lg flex items-center gap-1 md:gap-1.5 shadow-sm text-xs md:text-sm"
                                >
                                    <Stethoscope className="h-2.5 w-2.5 md:h-3 md:w-3" />
                                    <span>{specialty}</span>
                                    <button
                                        onClick={() => handleSpecialtyToggle(specialty)}
                                        className="ml-1 text-blue-500 hover:text-blue-700 rounded-full w-3 h-3 md:w-4 md:h-4 inline-flex items-center justify-center hover:bg-blue-100 transition-colors"
                                    >
                                        <X className="h-2 w-2 md:h-2.5 md:w-2.5" />
                                    </button>
                                </Badge>
                            ))}
                        </div>
                    </div>
                )}

                {/* Empty State */}
                {newDoctor.specialties.length === 0 && (
                    <div className="bg-slate-50 border border-slate-200 rounded-xl p-4 text-center">
                        <Stethoscope className="h-6 w-6 md:h-8 md:w-8 mx-auto mb-2 text-slate-400" />
                        <p className="text-sm md:text-base text-slate-600">No hay especialidades seleccionadas</p>
                        <p className="text-xs md:text-sm text-slate-500">Seleccione las especialidades de la lista arriba</p>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <Dialog open={isNewDoctorDialogOpen} onOpenChange={handleClose}>
            <DialogContent className="w-[95vw] md:w-[92vw] max-w-4xl max-h-[95vh] md:max-h-[90vh] overflow-y-auto p-4 md:p-6 lg:p-8 rounded-xl md:rounded-2xl border border-slate-200 shadow-2xl shadow-slate-400/20 bg-white">
                <DialogHeader>
                    {/* Step Progress Indicator */}
                    <div className="flex items-center justify-center space-x-2 md:space-x-4 mb-4">
                        {[1, 2].map((step) => (
                            <div key={step} className="flex items-center">
                                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-xs md:text-sm font-semibold transition-all duration-300 ${
                                    step === currentStep
                                        ? 'bg-gradient-to-br from-blue-500 to-sky-600 text-white shadow-lg shadow-blue-500/30 border border-blue-400'
                                        : step < currentStep
                                            ? 'bg-gradient-to-br from-slate-100 to-slate-200 text-slate-700 shadow-md shadow-slate-400/20 border border-slate-300'
                                            : 'bg-gradient-to-br from-slate-50 to-slate-100 text-slate-400 shadow-sm shadow-slate-300/20 border border-slate-200'
                                }`}>
                                    {step < currentStep ? <Check className="w-3 h-3 md:w-4 md:h-4" /> : step}
                                </div>
                                {step < 2 && (
                                    <div className={`w-8 md:w-16 h-0.5 mx-2 md:mx-3 rounded-full transition-all duration-300 ${
                                        step < currentStep
                                            ? 'bg-gradient-to-r from-slate-300 to-slate-400 shadow-sm'
                                            : 'bg-gradient-to-r from-slate-200 to-slate-300'
                                    }`} />
                                )}
                            </div>
                        ))}
                    </div>
                </DialogHeader>

                {/* Step Content */}
                <div className="py-4 md:py-6">
                    {currentStep === 1 && renderStep1()}
                    {currentStep === 2 && renderStep2()}
                </div>

                {/* Navigation Buttons */}
                <div className="flex flex-col gap-3 pt-4 md:pt-6 border-t border-slate-200">
                    {/* Anterior button - always on top when visible */}
                    {currentStep > 1 && (
                        <div className="flex justify-start">
                            <Button
                                variant="outline"
                                onClick={handlePrevious}
                                disabled={isProcessing}
                                className="w-full sm:w-auto rounded-xl text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50"
                            >
                                <ChevronLeft className="w-4 h-4 mr-2" />
                                Anterior
                            </Button>
                        </div>
                    )}

                    {/* Cancelar and Action buttons - side by side */}
                    <div className="flex gap-2 sm:gap-3">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            disabled={isProcessing}
                            className="flex-1 sm:flex-none rounded-xl text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50"
                        >
                            Cancelar
                        </Button>

                        {currentStep < 2 ? (
                            <Button
                                onClick={handleNext}
                                disabled={isProcessing || !isStepValid(currentStep)}
                                className="flex-1 sm:flex-none rounded-xl bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Seleccionar especialidades
                                <ChevronRight className="w-4 h-4 ml-2" />
                            </Button>
                        ) : (
                            <Button
                                onClick={handleCreateNewDoctor}
                                disabled={isProcessing || !isStepValid(currentStep)}
                                className="flex-1 sm:flex-none rounded-xl bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isProcessing ? 'Procesando...' : (
                                    <>
                                        {searchResult === "found" ? (
                                            <>
                                                <Plus className="h-4 w-4 mr-2"/>
                                                Agregar al Establecimiento
                                            </>
                                        ) : (
                                            <>
                                                <Plus className="h-4 w-4 mr-2"/>
                                                Crear y Agregar
                                            </>
                                        )}
                                    </>
                                )}
                            </Button>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}