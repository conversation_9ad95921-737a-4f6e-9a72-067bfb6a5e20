import {useCallback, useEffect, useRef, useState} from 'react'

interface MedicalCenterSSEOptions {
    medicalCenterId: number
    employeeUserId: number
}

export interface SSEEvent {
    type: string
    data: unknown
    timestamp: Date
}

export const useMedicalCenterSSE = (options: MedicalCenterSSEOptions) => {
    const {medicalCenterId, employeeUserId} = options
    const [isConnected, setIsConnected] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null)
    const [backendUrl, setBackendUrl] = useState<string>("")

    const eventSourceRef = useRef<EventSource | null>(null)
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const eventListenersRef = useRef<Map<string, ((data: unknown) => void)[]>>(new Map())
    const isConnectingRef = useRef(false)
    const isMountedRef = useRef(true)
    const reconnectAttemptsRef = useRef(0)

    // Fetch backend URL from the frontend server
    const fetchBackendUrl = useCallback(async () => {
        try {
            const response = await fetch('/api/config')
            if (response.ok) {
                const config = await response.json()
                setBackendUrl(config.backendUrl || "")
            } else {
                console.error('Failed to fetch backend URL from config API')
            }
        } catch (error) {
            console.error('Error fetching backend URL:', error)
        }
    }, [])

    // Get backend URL from state
    const getBackendUrl = useCallback(() => {
        return backendUrl
    }, [backendUrl])

    const addEventListener = useCallback((eventType: string, callback: (data: unknown) => void) => {
        const listeners = eventListenersRef.current.get(eventType) || []
        const isFirstListener = listeners.length === 0
        listeners.push(callback)
        eventListenersRef.current.set(eventType, listeners)

        if (eventSourceRef.current && isFirstListener && eventType !== 'connection' && eventType !== 'error' && eventType !== 'message') {
            const handleEvent = (event: MessageEvent) => {
                if (!isMountedRef.current) return

                try {
                    const data = JSON.parse(event.data)
                    emitEvent(eventType, data)
                } catch (e) {
                    emitEvent(eventType, event.data)
                }
            }

            eventSourceRef.current.addEventListener(eventType, handleEvent)
        }

        return () => {
            const updatedListeners = eventListenersRef.current.get(eventType)?.filter(cb => cb !== callback) || []
            if (updatedListeners.length === 0) {
                eventListenersRef.current.delete(eventType)
            } else {
                eventListenersRef.current.set(eventType, updatedListeners)
            }
        }
    }, [])

    const emitEvent = useCallback((eventType: string, data: unknown) => {
        if (!isMountedRef.current) return

        const listeners = eventListenersRef.current.get(eventType) || []
        const event: SSEEvent = {
            type: eventType,
            data,
            timestamp: new Date()
        }
        setLastEvent(event)
        listeners.forEach(callback => {
            try {
                callback(data)
            } catch (error) {
                console.error(`Error in event listener for ${eventType}:`, error)
            }
        })
    }, [])

    const disconnect = useCallback(() => {
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current)
            reconnectTimeoutRef.current = null
        }

        if (eventSourceRef.current) {
            eventSourceRef.current.close()
            eventSourceRef.current = null
        }

        isConnectingRef.current = false
        reconnectAttemptsRef.current = 0

        if (isMountedRef.current) {
            setIsConnected(false)
            emitEvent('connection', {status: 'disconnected'})
        }
    }, [emitEvent])

    const connect = useCallback(() => {
        if (isConnectingRef.current || !isMountedRef.current) {
            return
        }

        if (!medicalCenterId) {
            console.warn('Cannot connect: medicalCenterId is missing')
            return
        }

        isConnectingRef.current = true

        try {
            disconnect()

            // Connect directly to Quarkus backend
            const backendBaseUrl = getBackendUrl()
            const url = `${backendBaseUrl}/sse/data/${medicalCenterId}?employee-user-id=${employeeUserId}`

            console.log('Connecting directly to backend SSE:', url)

            const eventSource = new EventSource(url)
            eventSourceRef.current = eventSource

            eventSource.onopen = () => {
                if (!isMountedRef.current) return

                console.log('Direct backend SSE connection opened')
                isConnectingRef.current = false
                reconnectAttemptsRef.current = 0
                setIsConnected(true)
                setError(null)
                emitEvent('connection', {status: 'connected'})
            }

            // Set up event listeners for existing registered event types
            eventListenersRef.current.forEach((listeners, eventType) => {
                if (eventType !== 'connection' && eventType !== 'error' && eventType !== 'message') {
                    const handleEvent = (event: MessageEvent) => {
                        if (!isMountedRef.current) return

                        try {
                            const data = JSON.parse(event.data)
                            emitEvent(eventType, data)
                        } catch (e) {
                            emitEvent(eventType, event.data)
                        }
                    }

                    eventSource.addEventListener(eventType, handleEvent)
                }
            })

            // Set up heartbeat listener to keep connection alive
            eventSource.addEventListener('heartbeat', () => {
                // Just keep alive, reset reconnect attempts
                reconnectAttemptsRef.current = 0
            })

            eventSource.onmessage = (event) => {
                if (!isMountedRef.current) return
                try {
                    const data = JSON.parse(event.data)
                    emitEvent('message', data)
                } catch (e) {
                    emitEvent('message', event.data)
                }
            }

            eventSource.onerror = (event) => {
                if (!isMountedRef.current) return

                console.error('Direct backend SSE error:', event)
                isConnectingRef.current = false
                setIsConnected(false)

                reconnectAttemptsRef.current++
                const maxAttempts = 10

                if (reconnectAttemptsRef.current >= maxAttempts) {
                    setError(`Max reconnection attempts reached (${maxAttempts})`)
                    emitEvent('error', {error: 'Max reconnection attempts reached'})
                    return
                }

                setError('Connection error - attempting reconnect')
                emitEvent('error', {error: 'Connection error', attempt: reconnectAttemptsRef.current})

                // Exponential backoff for reconnection
                const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current - 1), 30000)

                if (isMountedRef.current) {
                    reconnectTimeoutRef.current = setTimeout(() => {
                        if (isMountedRef.current) {
                            console.log(`Reconnecting to backend SSE (attempt ${reconnectAttemptsRef.current})...`)
                            connect()
                        }
                    }, delay)
                }
            }

        } catch (err) {
            console.error('Failed to create direct backend SSE connection:', err)
            isConnectingRef.current = false
            if (isMountedRef.current) {
                setError('Failed to connect to backend')
                emitEvent('error', {error: 'Failed to connect to backend'})
            }
        }
    }, [medicalCenterId, employeeUserId, disconnect, emitEvent, getBackendUrl])

    // Fetch backend URL on mount
    useEffect(() => {
        fetchBackendUrl()
    }, [fetchBackendUrl])

    useEffect(() => {
        isMountedRef.current = true

        if (medicalCenterId && backendUrl) {
            connect()
        }

        return () => {
            isMountedRef.current = false
            disconnect()
        }
    }, [medicalCenterId, employeeUserId, backendUrl, connect, disconnect])

    const reconnect = useCallback(() => {
        reconnectAttemptsRef.current = 0
        disconnect()
        setTimeout(() => {
            if (isMountedRef.current) {
                connect()
            }
        }, 100)
    }, [connect, disconnect])

    return {
        isConnected,
        error,
        lastEvent,
        addEventListener,
        disconnect,
        reconnect,
        reconnectAttempts: reconnectAttemptsRef.current
    }
}